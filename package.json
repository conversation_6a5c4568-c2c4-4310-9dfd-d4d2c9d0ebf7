{"name": "xpcli", "version": "0.10.0", "description": "Latam EBIZ CLI to create Java libs, microservices or BPM.", "main": "bin/index.js", "publishConfig": {"registry": "https://artifactoryrepo1.appslatam.com/artifactory/api/npm/npm-local/"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"]}, "files": ["bin", "dist"], "scripts": {"test": "jest", "build": "rollup -c", "deploy": "npm publish"}, "repository": {"type": "git", "url": "**************:latamairlines/paxc/ebz/xplibraries/xp-cli.git"}, "bin": "./bin/index.cjs", "keywords": ["cli", "client", "tools", "platform"], "author": {"name": "Squad as a Service (SAS)", "url": "https://latamxp.slack.com/archives/C04LLRB2UCU"}, "license": "ISC", "dependencies": {"chalk": "^4.1.2", "commander": "^11.1.0", "console-clear": "^1.1.1", "fs-extra": "^11.1.1", "gradle-to-js": "^2.0.0", "gradlejs": "^1.0.0", "inquirer": "^9.2.2", "js-yaml": "^4.1.0", "make-dir": "^4.0.0", "recursive-readdir": "^2.2.2", "replace-in-file": "^5.0.0", "simple-git": "^3.27.0", "validate-npm-package-name": "^5.0.0"}, "devDependencies": {"jest": "^29.7.0", "rollup": "^4.12.0", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.2.2", "supertest": "^6.3.3"}, "type": "module", "overrides": {"underscore": "1.12.1", "glob": "10.4.5"}}